from connect_to_supabase import db_cursor, db_conn, ensure_connection
import psycopg2
from typing import Dict, Any, List

def insert_db_rec_count() -> None:
    """
    Insert unique taluka-village combinations from search_query into records_count
    Args: None
    Returns: None
    """
    ensure_connection()
    sql = """
        INSERT INTO records_count (taluka, village)
        SELECT taluka, village
        FROM search_query as sq
        GROUP BY taluka, village
        WHERE taluka IS NOT NULL AND village IS NOT NULL
        ON CONFLICT (taluka, village) DO NOTHING;
    """
    db_cursor.execute(sql)
    db_conn.commit()
    print("Inserted unique taluka-village combinations into records_count")


def update_db_rec_count() -> None:
    """
    Update records_count with the number of records based on search_query data
    Args: None
    Returns: None
    """
    ensure_connection()
    sql = """
        UPDATE records_count
        SET db_rec_count = subquery.rec_count
        FROM (
            SELECT taluka, village, COUNT(*) as rec_count
            FROM search_query
            GROUP BY taluka, village
        ) AS subquery
        WHERE records_count.taluka = subquery.taluka
        AND records_count.village = subquery.village;
    """
    db_cursor.execute(sql)
    db_conn.commit()
    print("Updated records_count with the number of records based on search_query data")


def update_goa_rec_count(taluka: str, village: str, rec_count: int) -> None:
    """
    Update Goa Record count for specific taluka-village combination
    Args:
        taluka (str): Taluka name
        village (str): Village name
        rec_count (int): Record count
    Returns: None
    """
    ensure_connection()
    sql = """
        UPDATE records_count
        SET 
            goa_rec_count = %s
            latest_check = CURRENT_TIMESTAMP
        WHERE 
            taluka = %s AND village = %s;
    """
    db_cursor.execute(sql, (rec_count, taluka, village))
    db_conn.commit()
    print(f"Updated Goa record count for {taluka}, {village}")


def is_existing_in_db(taluka: str, village: str, survey_num: str, subdivision_num: str) -> bool:
    """
    Check if a search query record exists in the database
    Args:
        taluka (str): Taluka name
        village (str): Village name
        survey_num (str): Survey number
        subdivision_num (str): Subdivision number
    Returns:
        bool: True if record exists, False otherwise
    """
    ensure_connection()
    sql = """
        SELECT 1
        FROM search_query
        WHERE taluka = %s AND village = %s AND survey_num = %s AND subdivision_num = %s;
    """
    db_cursor.execute(sql, (taluka, village, survey_num, subdivision_num))
    return db_cursor.fetchone() is not None


def add_search_query(taluka: str, village: str, survey_num: str, subdivision_num: str) -> None:
    """
    Add transaction deed records to the database
    Args:
        taluka (str): Taluka name
        village (str): Village name
        survey_num (str): Survey number
        subdivision_num (str): Subdivision number
    Returns: None
    """
    ensure_connection()
    search_id = f"{taluka}_{village}_{survey_num}_{subdivision_num}"
    sql = """
        INSERT INTO search_query (search_id, taluka, village, survey_num, subdivision_num)
        VALUES (%s, %s, %s, %s, %s);
    """
    db_cursor.execute(sql, (search_id, taluka, village, survey_num, subdivision_num))
    db_conn.commit()
    print(f"Added search_query: {search_id}")


def add_transaction_deed(search_id: str, parsed_data: Dict[str, Any]) -> None:
    """
    Add transaction deed records to the database
    Args:
        search_id (str): Search ID
        parsed_data (Dict[str, Any]): Parsed data
    Returns: None
    """
    ensure_connection()
    for i in parsed_data["transaction_deed"]:
        cols = list(i.keys())
        cols.insert(0, "search_id")
        vals = list(i.values())
        vals.insert(0, search_id)
        vals_fixed = []
        for i in vals:
            if isinstance(i, list):
                vals_fixed.append(", ".join(i))
            else:
                vals_fixed.append(i)
    
        sql = "INSERT INTO transaction_deed ({}) VALUES ({})".format(', '.join(cols), ', '.join(['%s'] * len(cols)))
        db_cursor.execute(sql, vals_fixed)
    
    db_conn.commit()
    print(f"Added transaction_deed: {search_id}")


def add_ror_public(search_id: str, parsed_data: Dict[str, Any]) -> None:
    """
    Add ror public records to the database
    Args:
        search_id (str): Search ID

        parsed_data (Dict[str, Any]): Parsed data
    Returns: None
    """
    ensure_connection()
    i = parsed_data["ror_public"]
    cols = list(i.keys())
    cols.insert(0, "search_id")
    vals = list(i.values())
    vals.insert(0, search_id)
    vals_fixed = []
    for i in vals:
        if isinstance(i, list):
            vals_fixed.append(", ".join(i))
        else:
            vals_fixed.append(i)

    sql = "INSERT INTO ror_public ({}) VALUES ({})".format(', '.join(cols), ', '.join(['%s'] * len(cols)))
    db_cursor.execute(sql, vals_fixed)
    db_conn.commit()
    print(f"Added ror_public: {search_id}")


def add_ror_cultivable(search_id: str, parsed_data: Dict[str, Any]) -> None:
    """
    Add ror cultivable records to the database
    Args:
        search_id (str): Search ID
        parsed_data (Dict[str, Any]): Parsed data
    Returns: None
    """
    ensure_connection()
    i = parsed_data["ror_public_details"]["cultivable"]
    cols = list(i.keys())
    cols.insert(0, "search_id")
    vals = list(i.values())
    vals.insert(0, search_id)
    vals_fixed = []
    for i in vals:
        if isinstance(i, list):
            vals_fixed.append(", ".join(i))
        else:
            vals_fixed.append(i)

    sql = "INSERT INTO ror_cultivable ({}) VALUES ({})".format(', '.join(cols), ', '.join(['%s'] * len(cols)))
    db_cursor.execute(sql, vals_fixed)
    db_conn.commit()
    print(f"Added ror_cultivable: {search_id}")


def add_ror_uncultivable(search_id: str, parsed_data: Dict[str, Any]) -> None:
    """
    Add ror uncultivable records to the database
    Args:
        search_id (str): Search ID
        parsed_data (Dict[str, Any]): Parsed data
    Returns: None
    """
    ensure_connection()
    i = parsed_data["ror_public_details"]["uncultivable"]
    cols = list(i.keys())
    cols.insert(0, "search_id")
    vals = list(i.values())
    vals.insert(0, search_id)
    vals_fixed = []
    for i in vals:
        if isinstance(i, list):
            vals_fixed.append(", ".join(i))
        else:
            vals_fixed.append(i)

    sql = "INSERT INTO ror_uncultivable ({}) VALUES ({})".format(', '.join(cols), ', '.join(['%s'] * len(cols)))
    db_cursor.execute(sql, vals_fixed)
    db_conn.commit()
    print(f"Added ror_uncultivable: {search_id}")


def add_ror_data_1(search_id: str, parsed_data: Dict[str, Any]) -> None:
    """
    Add ror data 1 records to the database
    Args:
        search_id (str): Search ID
        parsed_data (Dict[str, Any]): Parsed data
    Returns: None
    """
    ensure_connection()
    temp_vals = parsed_data["ror_public_details"]["data_1"]["assessment_rs"]
    row_count = len(temp_vals)
    ror_data_1 = parsed_data["ror_public_details"]["data_1"]
    for row_idx in range(row_count):
        cols = list(ror_data_1.keys())
        cols.insert(0, "search_id")
        vals = list(ror_data_1.values())
        vals.insert(0, [search_id] * row_count)
        vals_fixed = []
        for list_i in vals:
            try:
                vals_fixed.append(list_i[row_idx])
            except IndexError:
                vals_fixed.append("")

        sql = "INSERT INTO ror_data_1 ({}) VALUES ({})".format(', '.join(cols), ', '.join(['%s'] * len(cols)))
        db_cursor.execute(sql, vals_fixed)
        db_conn.commit()

    print(f"Added ror_data_1: {search_id}")


def add_ror_data2(search_id: str, parsed_data: Dict[str, Any]) -> None:
    """
    Add ror data 2 records to the database
    Args:
        search_id (str): Search ID
        parsed_data (Dict[str, Any]): Parsed data
    Returns: None
    """
    ensure_connection()
    temp_vals = parsed_data["ror_public_details"]["data_2"]["sr_num"]
    row_count = len(temp_vals)
    ror_data_2 = parsed_data["ror_public_details"]["data_2"]
    for row_idx in range(row_count):
        cols = list(ror_data_2.keys())
        cols.insert(0, "search_id")
        vals = list(ror_data_2.values())
        vals.insert(0, [search_id] * row_count)
        vals_fixed = []
        for list_i in vals:
            try:
                vals_fixed.append(list_i[row_idx])
            except IndexError:
                vals_fixed.append("")

        sql = "INSERT INTO ror_data_2 ({}) VALUES ({})".format(', '.join(cols), ', '.join(['%s'] * len(cols)))
        db_cursor.execute(sql, vals_fixed)
        db_conn.commit()

    print(f"Added ror_data_2: {search_id}")


def add_ror_data3(search_id: str, parsed_data: Dict[str, Any]) -> None:
    """
    Add ror data 3 records to the database
    Args:
        search_id (str): Search ID
        parsed_data (Dict[str, Any]): Parsed data
    Returns: None
    """
    ensure_connection()
    temp_vals = parsed_data["ror_public_details"]["data_3"]["sr_num"]
    row_count = len(temp_vals)
    ror_data_3 = parsed_data["ror_public_details"]["data_3"]
    for row_idx in range(row_count):
        cols = list(ror_data_3.keys())
        cols.insert(0, "search_id")
        vals = list(ror_data_3.values())
        vals.insert(0, [search_id] * row_count)
        vals_fixed = []
        for list_i in vals:
            try:
                vals_fixed.append(list_i[row_idx])
            except IndexError:
                vals_fixed.append("")

        sql = "INSERT INTO ror_data_3 ({}) VALUES ({})".format(', '.join(cols), ', '.join(['%s'] * len(cols)))
        db_cursor.execute(sql, vals_fixed)
        db_conn.commit()

    print(f"Added ror_data_3: {search_id}")


def add_ror_data4(search_id: str, parsed_data: Dict[str, Any]) -> None:
    """
    Add ror data 4 records to the database
    Args:
        search_id (str): Search ID
        parsed_data (Dict[str, Any]): Parsed data
    Returns: None
    """
    ensure_connection()
    temp_vals = parsed_data["ror_public_details"]["data_4"]["name_of_the_person_holding_rights_and_nature_of_rights"]
    row_count = len(temp_vals)
    ror_data_4 = parsed_data["ror_public_details"]["data_4"]
    for row_idx in range(row_count):
        cols = list(ror_data_4.keys())
        cols.insert(0, "search_id")
        vals = list(ror_data_4.values())
        vals.insert(0, [search_id] * row_count)
        vals_fixed = []
        for list_i in vals:
            try:
                vals_fixed.append(list_i[row_idx])
            except IndexError:
                vals_fixed.append("")

        sql = "INSERT INTO ror_data_4 ({}) VALUES ({})".format(', '.join(cols), ', '.join(['%s'] * len(cols)))
        db_cursor.execute(sql, vals_fixed)
        db_conn.commit()

    print(f"Added ror_data_4: {search_id}")


def add_ror_cropped(search_id: str, parsed_data: Dict[str, Any]) -> None:
    """
    Add ror cropped records to the database
    Args:
        search_id (str): Search ID
        parsed_data (Dict[str, Any]): Parsed data
    Returns: None
    """
    ensure_connection()
    temp_vals = parsed_data["ror_public_details"]["cropped"]["year"]
    row_count = len(temp_vals)
    ror_cropped = parsed_data["ror_public_details"]["cropped"]
    for row_idx in range(row_count):
        cols = list(ror_cropped.keys())
        cols.insert(0, "search_id")
        vals = list(ror_cropped.values())
        vals.insert(0, [search_id] * row_count)
        vals_fixed = []
        for list_i in vals:
            try:
                vals_fixed.append(list_i[row_idx])
            except IndexError:
                vals_fixed.append("")

        sql = "INSERT INTO ror_cropped ({}) VALUES ({})".format(', '.join(cols), ', '.join(['%s'] * len(cols)))
        db_cursor.execute(sql, vals_fixed)
        db_conn.commit()

    print(f"Added ror_cropped: {search_id}")


def add_ror_private(search_id: str, parsed_data: Dict[str, Any]) -> None:
    """
    Add ror private records to the database
    Args:
        search_id (str): Search ID
        parsed_data (Dict[str, Any]): Parsed data
    Returns: None
    """
    ensure_connection()
    i = parsed_data["ror_private"]
    cols = list(i.keys())
    cols.insert(0, "search_id")
    vals = list(i.values())
    vals.insert(0, search_id)
    vals_fixed = []
    for i in vals:
        if isinstance(i, list):
            vals_fixed.append(", ".join(i))
        else:
            vals_fixed.append(i)

    sql = "INSERT INTO ror_private ({}) VALUES ({})".format(', '.join(cols), ', '.join(['%s'] * len(cols)))
    db_cursor.execute(sql, vals_fixed)
    db_conn.commit()
    print(f"Added ror_private: {search_id}")


def add_ror_private_details(search_id: str, parsed_data: Dict[str, Any]) -> None:
    """
    Add ror private details records to the database
    Args:
        search_id (str): Search ID
        parsed_data (Dict[str, Any]): Parsed data
    Returns: None
    """
    ensure_connection()
    i = parsed_data["ror_private_details"]
    cols = list(i.keys())
    cols.insert(0, "search_id")
    vals = list(i.values())
    vals.insert(0, search_id)
    vals_fixed = []
    for i in vals:
        if isinstance(i, list):
            vals_fixed.append(", ".join(i))
        else:
            vals_fixed.append(i)

    sql = "INSERT INTO ror_private_details ({}) VALUES ({})".format(', '.join(cols), ', '.join(['%s'] * len(cols)))
    db_cursor.execute(sql, vals_fixed)
    db_conn.commit()
    print(f"Added ror_private_details: {search_id}")


def add_ror_private_table(search_id: str, parsed_data: Dict[str, any]) -> None:
    """
    Add ror private table records to the database
    Args:
        search_id (str): Search ID
        parsed_data (Dict[str, Any]): Parsed data
    Returns: None
    """
    ensure_connection()
    temp_vals = parsed_data["ror_private_details"]["private_table"]["mutation_date"]
    row_count = len(temp_vals)
    ror_private_table = parsed_data["ror_private_details"]["private_table"]
    for row_idx in range(row_count):
        cols = list(ror_private_table.keys())
        cols.insert(0, "search_id")
        vals = list(ror_private_table.values())
        vals.insert(0, [search_id] * row_count)
        vals_fixed = []
        for list_i in vals:
            try:
                vals_fixed.append(list_i[row_idx])
            except IndexError:
                vals_fixed.append("")

        sql = "INSERT INTO ror_private_table ({}) VALUES ({})".format(', '.join(cols), ', '.join(['%s'] * len(cols)))
        db_cursor.execute(sql, vals_fixed)
        db_conn.commit()

    print(f"Added ror_private_table: {search_id}")

