import cv2
import pytesseract
from pytesseract import Output
import numpy as np
from scipy import ndimage


def solve_captcha(img_fp):
    # pre-process img
    img = cv2.imread(img_fp, cv2.IMREAD_COLOR)
    img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    img = cv2.resize(img, None, fx=2, fy=2, interpolation=cv2.INTER_CUBIC)
    cv_size = lambda img: tuple(img.shape[1::-1])
    img_size = cv_size(img)

    img = cv2.GaussianBlur(img, (17, 17), 0)
    img = cv2.threshold(img, 220, 255, cv2.THRESH_BINARY)[1]
    ########################

    # try to solve captcha
    def is_solved(img):
        result = pytesseract.image_to_string(img, config="digits")
        if result:
            try:
                result = abs(int(result.strip()))
                if len(str(result)) == 6:
                    return result
                else:
                    return False
            except ValueError:
                return False
    ########################

    solved = is_solved(img)
    if solved:
        return solved
    else:
        angles_in_px = [90]
        for angle_in_px in angles_in_px:
            # angle_in_px = 90
            # print("ANGLE IN PX", angle_in_px)

            pt_A = [0, 0]
            pt_B = [angle_in_px, img_size[-1]]
            pt_C = [img_size[0], img_size[-1]]
            pt_D = [img_size[0] - angle_in_px, 0]

            width_AD = np.sqrt(((pt_A[0] - pt_D[0]) ** 2) + ((pt_A[1] - pt_D[1]) ** 2))
            width_BC = np.sqrt(((pt_B[0] - pt_C[0]) ** 2) + ((pt_B[1] - pt_C[1]) ** 2))
            maxWidth = max(int(width_AD), int(width_BC))


            height_AB = np.sqrt(((pt_A[0] - pt_B[0]) ** 2) + ((pt_A[1] - pt_B[1]) ** 2))
            height_CD = np.sqrt(((pt_C[0] - pt_D[0]) ** 2) + ((pt_C[1] - pt_D[1]) ** 2))
            maxHeight = max(int(height_AB), int(height_CD))

            input_pts = np.float32([pt_A, pt_B, pt_C, pt_D])
            output_pts = np.float32([[0, 0],
                                    [0, maxHeight - 1],
                                    [maxWidth - 1, maxHeight - 1],
                                    [maxWidth - 1, 0]])

            M = cv2.getPerspectiveTransform(input_pts,output_pts)
            img = cv2.warpPerspective(img,M,(maxWidth, maxHeight),flags=cv2.INTER_LINEAR)
            # cv2.imshow("Result", img)
            # cv2.waitKey(0)

            solved = is_solved(img)
            if solved:
                return solved
            else:
                pt_A = [angle_in_px, 0]
                pt_B = [0, img_size[-1]]
                pt_C = [img_size[0] - angle_in_px, img_size[-1]]
                pt_D = [img_size[0], 0]

                width_AD = np.sqrt(((pt_A[0] - pt_D[0]) ** 2) + ((pt_A[1] - pt_D[1]) ** 2))
                width_BC = np.sqrt(((pt_B[0] - pt_C[0]) ** 2) + ((pt_B[1] - pt_C[1]) ** 2))
                maxWidth = max(int(width_AD), int(width_BC))


                height_AB = np.sqrt(((pt_A[0] - pt_B[0]) ** 2) + ((pt_A[1] - pt_B[1]) ** 2))
                height_CD = np.sqrt(((pt_C[0] - pt_D[0]) ** 2) + ((pt_C[1] - pt_D[1]) ** 2))
                maxHeight = max(int(height_AB), int(height_CD))

                input_pts = np.float32([pt_A, pt_B, pt_C, pt_D])
                output_pts = np.float32([[0, 0],
                                        [0, maxHeight - 1],
                                        [maxWidth - 1, maxHeight - 1],
                                        [maxWidth - 1, 0]])

                M = cv2.getPerspectiveTransform(input_pts,output_pts)
                img = cv2.warpPerspective(img,M,(maxWidth, maxHeight),flags=cv2.INTER_LINEAR)
                # cv2.imshow("Result", img)
                # cv2.waitKey(0)
                solved = is_solved(img)
                if solved:
                    return solved
        return False
    