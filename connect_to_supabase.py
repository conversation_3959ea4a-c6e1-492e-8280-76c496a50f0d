import psycopg2
import psycopg2.extras
import yaml
import os
from typing import Optional


# Global variables for database connection and cursor
db_conn = None
db_cursor = None


def load_config():
    """
    Load database configuration from config file
    
    Args: None
    Returns:
        dict: A dictionary with database configuration (e.g., host, user, password, etc.)
    """
    try:
        with open("./supabase_config.yml", "r") as f:
            return yaml.safe_load(f)
    except FileNotFoundError:
        raise Exception("Please set environment variables or create supabase_config.yml")

db_config = load_config()

def connect_to_database():
    """
    Establish connection to Supabase PostgreSQL database
    Args: None
    Returns: None
    """
    global db_conn, db_cursor
    try:
        db_conn = psycopg2.connect(**db_config)
        db_cursor = db_conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        print("Connected to Supabase PostgreSQL database")

    except psycopg2.Error as err:
        print(f"Database connection error: {err}")
        raise


def ensure_connection():
    """
    Ensure database connection is active
    Args: None
    Returns: None
    """
    global db_conn, db_cursor
    if db_conn is None or db_conn.closed:
        connect_to_database()

    # Test conection
    try:
        db_cursor.execute("SELECT 1")
        db_cursor.fetchone()
    except psycopg2.Error:
        connect_to_database()


def main():
    try:
        connect_to_database()
    except Exception as e:
        print(f"Failed to connect to database on import: {e}")
        print("Please ensure your Supabase credentials are configured correctly.")

if __name__ == "__main__":
    main()